extends Control

# Test skript pre overenie FontLoader v dialógoch

func _ready():
	print("=== TEST DIALOGUE FONTS ===")
	test_font_loading()

func test_font_loading():
	"""Testuje načítanie fontov pre dialógy"""
	
	# Test narrator font
	print("\n--- NARRATOR FONT TEST ---")
	var narrator_font = FontLoader.create_font_variation("narrator_text")
	if narrator_font:
		print("✅ Narrator font úspešne vytvorený")
		print("   - Base font: ", narrator_font.base_font)
		print("   - Size: ", FontLoader.get_font_config("narrator_text").size)
		print("   - Color: ", FontLoader.get_font_config("narrator_text").color)
	else:
		print("❌ Chyba pri vytváraní narrator fontu")
	
	# Test character dialogue font
	print("\n--- CHARACTER DIALOGUE FONT TEST ---")
	var dialogue_font = FontLoader.create_font_variation("character_dialogue")
	if dialogue_font:
		print("✅ Character dialogue font úspešne vytvorený")
		print("   - Base font: ", dialogue_font.base_font)
		print("   - Size: ", FontLoader.get_font_config("character_dialogue").size)
		print("   - Color: ", FontLoader.get_font_config("character_dialogue").color)
	else:
		print("❌ Chyba pri vytváraní character dialogue fontu")
	
	# Test UI elements font
	print("\n--- UI ELEMENTS FONT TEST ---")
	var ui_font = FontLoader.create_font_variation("ui_elements")
	if ui_font:
		print("✅ UI elements font úspešne vytvorený")
		print("   - Base font: ", ui_font.base_font)
		print("   - Size: ", FontLoader.get_font_config("ui_elements").size)
		print("   - Color: ", FontLoader.get_font_config("ui_elements").color)
	else:
		print("❌ Chyba pri vytváraní UI elements fontu")
	
	print("\n=== TEST DOKONČENÝ ===")
