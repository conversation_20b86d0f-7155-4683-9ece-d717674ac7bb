[folding]

node_unfolds=[Node<PERSON>ath("."), PackedStringArray("Layout"), NodePath("Background"), PackedStringArray("Layout"), NodePath("TitleOverlay"), PackedStringArray("Layout"), NodePath("TitleOverlay/TitleLabel"), PackedStringArray("Layout", "Theme Overrides"), NodePath("MenuContainer"), PackedStringArray("Layout"), NodePath("MenuContainer/ButtonContainer"), PackedStringArray("Layout"), NodePath("MenuContainer/ButtonContainer/NovaHraButton"), PackedStringArray("Layout", "Textures"), NodePath("MenuContainer/ButtonContainer/NovaHraButton/NovaHraLabel"), PackedStringArray("Layout", "Theme Overrides"), NodePath("MenuContainer/ButtonContainer/Spacer1"), PackedStringArray("Layout"), <PERSON>de<PERSON><PERSON>("MenuContainer/ButtonContainer/PokracovatButton"), PackedStringArray("Layout", "Textures"), NodePath("MenuContainer/ButtonContainer/PokracovatButton/PokracovatLabel"), PackedStringArray("Layout", "Theme Overrides"), NodePath("MenuContainer/ButtonContainer/Spacer2"), PackedStringArray("Layout"), NodePath("MenuContainer/ButtonContainer/KapitolyButton"), PackedStringArray("Layout", "Textures"), NodePath("MenuContainer/ButtonContainer/KapitolyButton/KapitolyLabel"), PackedStringArray("Layout", "Theme Overrides"), NodePath("MenuContainer/ButtonContainer/Spacer3"), PackedStringArray("Layout"), NodePath("MenuContainer/ButtonContainer/NastaveniaButton"), PackedStringArray("Layout", "Textures"), NodePath("MenuContainer/ButtonContainer/NastaveniaButton/NastaveniaLabel"), PackedStringArray("Layout", "Theme Overrides"), NodePath("MenuContainer/ButtonContainer/Spacer4"), PackedStringArray("Layout"), NodePath("MenuContainer/ButtonContainer/OHreButton"), PackedStringArray("Layout", "Textures"), NodePath("MenuContainer/ButtonContainer/OHreButton/OHreLabel"), PackedStringArray("Layout", "Theme Overrides"), NodePath("VersionLabel"), PackedStringArray("Layout", "Theme Overrides")]
resource_unfolds=[]
nodes_folded=[]
