[res://scripts/NewChaptersMenu.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 105,
"scroll_position": 89.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/DialogueSystem.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 1123,
"scroll_position": 1107.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/VampireArithmeticPuzzle.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 12,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}
